"""
Zappa-optimized entry point for ReplyPal FastAPI application.
This module provides the FastAPI application wrapped with Mangum for AWS Lambda deployment.
"""

import os
import sys
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    # Import the FastAPI application
    from app.main import app as fastapi_app
    from mangum import Mangum

    # Wrap FastAPI app with Mangum for AWS Lambda
    app = Mangum(fastapi_app, lifespan="off")

    print("=" * 50)
    print("ZAPPA APP INITIALIZED SUCCESSFULLY")
    print("Using FastAPI application with Mangum adapter")
    print(f"Environment: {os.getenv('ENVIRONMENT', 'unknown')}")
    print("=" * 50)

except ImportError as e:
    print(f"Error importing FastAPI app: {e}")
    # Fallback to a simple Flask app if FastAPI import fails
    from flask import Flask, jsonify

    app = Flask(__name__)

    @app.route("/")
    def root():
        """Root endpoint for health check."""
        return jsonify({
            "status": "error",
            "message": "FastAPI import failed, using fallback Flask app",
            "version": os.getenv("APP_VERSION", "1.0.0"),
            "error": str(e)
        })

    @app.route("/ping")
    def ping():
        """Ping endpoint for health check."""
        return jsonify({"status": "error", "message": "FastAPI not available"})

    print("=" * 50)
    print("FALLBACK FLASK APP INITIALIZED")
    print(f"FastAPI import error: {e}")
    print("=" * 50)
