/**
 * ReplyPal - Background Script
 * Handles extension initialization, context menu, and message passing
 */

// Track tabs with content scripts ready
const readyTabs = new Set();

// Initialize context menu when extension is installed or updated
chrome.runtime.onInstalled.addListener(() => {
  // Create context menu item
  chrome.contextMenus.create({
    id: 'replypal',
    title: 'Generate with ReplyPal',
    contexts: ['selection', 'page']
  });

  // Initialize settings using centralized configuration
  // Note: We need to wait for config.js to load, but since this is a service worker,
  // we'll use the traditional approach here and rely on config.js for other files
  chrome.storage.local.get('settings', (data) => {
    if (!data.settings) {
      chrome.storage.local.set({
        settings: {
          environment: 'prod',
          saveHistory: true,
          useMockApi: false,
          hideFloatingIconGlobally: false,
          hiddenDomains: []
        }
      });
    } else {
      // Ensure new settings exist in existing settings
      const settings = data.settings;
      let needsUpdate = false;

      if (settings.hideFloatingIconGlobally === undefined) {
        settings.hideFloatingIconGlobally = false;
        needsUpdate = true;
      }

      if (!settings.hiddenDomains) {
        settings.hiddenDomains = [];
        needsUpdate = true;
      }

      // Force environment to 'prod' if it's currently 'local' (migration fix)
      if (settings.environment === 'local') {
        console.log('ReplyPal Background: Migrating environment from local to prod');
        settings.environment = 'prod';
        needsUpdate = true;
      }

      if (needsUpdate) {
        chrome.storage.local.set({ settings: settings });
      }
    }
  });
});

// Handle context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'replypal') {
    // Store the selected text if available
    if (info.selectionText) {
      chrome.storage.local.set({ selectedText: info.selectionText });
    } else {
      // Clear any previously selected text if no text is selected
      chrome.storage.local.remove('selectedText');
    }

    // Function to send message to content script
    const sendMessageToContentScript = () => {
      chrome.tabs.sendMessage(tab.id, {
        action: 'showPopup',
        text: info.selectionText || ''
      }).catch(err => {
        console.log('Error sending message to content script:', err);
      });
    };

    // Check if the tab is already ready
    if (readyTabs.has(tab.id)) {
      console.log(`Tab ${tab.id} already has content script ready`);
      sendMessageToContentScript();
    } else {
      console.log(`Injecting content script into tab ${tab.id}`);
      // Inject the content script
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        files: ['js/content.js']
      }, () => {
        // Wait for the content script to initialize
        const maxWaitTime = 1000; // 1 second
        const startTime = Date.now();

        const checkReady = () => {
          if (readyTabs.has(tab.id)) {
            console.log(`Tab ${tab.id} is now ready, sending message`);
            sendMessageToContentScript();
          } else if (Date.now() - startTime < maxWaitTime) {
            // Still within wait time, check again in 100ms
            setTimeout(checkReady, 100);
          } else {
            // Timeout reached, try sending message anyway
            console.log(`Timeout reached for tab ${tab.id}, sending message anyway`);
            sendMessageToContentScript();
          }
        };

        checkReady();
      });
    }
  }
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  // Track when content scripts are ready
  if (message.action === 'contentScriptReady' && sender.tab) {
    console.log(`Content script ready in tab ${sender.tab.id}`);
    readyTabs.add(sender.tab.id);
    sendResponse({ success: true });
  }
  else if (message.action === 'storeSelectedText') {
    // Store the selected text
    chrome.storage.local.set({ selectedText: message.text });
    sendResponse({ success: true });
  }
  else if (message.action === 'generateFromContent') {
    // Forward to API
    handleGenerateRequest(message.data)
      .then(response => {
        sendResponse({ success: true, data: response });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicate async response
  }
  else if (message.action === 'generateStreamingResponse') {
    // Handle streaming response
    handleStreamingRequest(message.data, sender.tab.id)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicate async response
  }
  else if (message.action === 'copyToClipboard') {
    // Copy text to clipboard
    copyToClipboard(message.text)
      .then(() => {
        sendResponse({ success: true });
      })
      .catch(error => {
        sendResponse({ success: false, error: error.message });
      });
    return true; // Indicate async response
  }
  else if (message.action === 'authenticateWithGoogle') {
    // Handle Google authentication
    chrome.identity.getAuthToken({ interactive: true }, (token) => {
      if (chrome.runtime.lastError) {
        sendResponse({ success: false, error: chrome.runtime.lastError.message });
      } else {
        // Get user info using the token
        fetch('https://www.googleapis.com/oauth2/v1/userinfo?alt=json', {
          headers: { Authorization: `Bearer ${token}` }
        })
          .then(response => response.json())
          .then(userInfo => {
            // Get API token from backend
            getApiToken({
              uid: userInfo.id,
              email: userInfo.email,
              name: userInfo.name,
              picture: userInfo.picture
            })
              .then(tokenResponse => {
                // Store user info and API token
                console.log('Received API token from backend:', tokenResponse);
                const userToStore = {
                  uid: userInfo.id,
                  displayName: userInfo.name,
                  email: userInfo.email,
                  photoURL: userInfo.picture,
                  apiToken: tokenResponse.access_token
                };
                console.log('Storing user data with token:', userToStore);
                chrome.storage.local.set({
                  user: userToStore
                }, () => {
                  if (chrome.runtime.lastError) {
                    console.error('Error storing user data:', chrome.runtime.lastError);
                  } else {
                    console.log('User data with token stored successfully');
                  }
                });
                sendResponse({ success: true, user: userInfo });
              })
              .catch(error => {
                console.error('Error getting API token:', error);
                // Still store user info without API token
                chrome.storage.local.set({
                  user: {
                    uid: userInfo.id,
                    displayName: userInfo.name,
                    email: userInfo.email,
                    photoURL: userInfo.picture
                  }
                });
                sendResponse({ success: true, user: userInfo });
              });
          })
          .catch(error => {
            sendResponse({ success: false, error: error.message });
          });
      }
    });
    return true; // Indicate async response
  }
  else if (message.action === 'signOut') {
    // Handle sign out
    chrome.identity.clearAllCachedAuthTokens(() => {
      chrome.storage.local.remove('user');
      sendResponse({ success: true });
    });
    return true; // Indicate async response
  }

  return true;
});

// Handle tab updates
chrome.tabs.onUpdated.addListener((tabId, changeInfo) => {
  if (changeInfo.status === 'loading') {
    readyTabs.delete(tabId);
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Clear any previously selected text
  chrome.storage.local.remove('selectedText');

  // Function to send message to content script
  const sendMessageToContentScript = () => {
    chrome.tabs.sendMessage(tab.id, {
      action: 'showPopup'
    }).catch(err => {
      console.log('Error sending message to content script:', err);
    });
  };

  // Check if the tab is already ready
  if (readyTabs.has(tab.id)) {
    console.log(`Tab ${tab.id} already has content script ready`);
    sendMessageToContentScript();
  } else {
    console.log(`Injecting content script into tab ${tab.id}`);
    // Inject the content script
    chrome.scripting.executeScript({
      target: { tabId: tab.id },
      files: ['js/content.js']
    }, () => {
      // Wait for the content script to initialize
      const maxWaitTime = 1000; // 1 second
      const startTime = Date.now();

      const checkReady = () => {
        if (readyTabs.has(tab.id)) {
          console.log(`Tab ${tab.id} is now ready, sending message`);
          sendMessageToContentScript();
        } else if (Date.now() - startTime < maxWaitTime) {
          // Still within wait time, check again in 100ms
          setTimeout(checkReady, 100);
        } else {
          // Timeout reached, try sending message anyway
          console.log(`Timeout reached for tab ${tab.id}, sending message anyway`);
          sendMessageToContentScript();
        }
      };

      checkReady();
    });
  }
});

/**
 * Handle generate request
 * @param {Object} requestData - Request data
 * @returns {Promise<Object>} Response data
 */
async function handleGenerateRequest(requestData) {
  try {
    // Get settings
    const settings = await getSettings();

    // Check if mock API is enabled
    if (settings.useMockApi) {
      return mockGenerateResponse(requestData);
    }

    // Get API URL
    const environment = settings.environment || 'prod';
    const apiUrls = {
      local: 'http://localhost:8000',
      dev: 'https://dev-api.replypal.com',
      prod: 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production'
    };
    const apiUrl = apiUrls[environment] || apiUrls.prod;

    // Get authentication token
    const userData = await getUserData();
    const authToken = userData?.apiToken;

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // Add authorization header if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    // Make API request
    const response = await fetch(`${apiUrl}/generate`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    console.log('Background script - API request URL:', `${apiUrl}/generate`);

    console.log('Background script - API response status:', response.status);
    console.log('Background script - API response text:', response.statusText);

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    // Don't log to console, just return the error to be handled by the caller
    throw error;
  }
}

/**
 * Handle streaming request
 * @param {Object} requestData - Request data
 * @param {number} tabId - Tab ID
 * @returns {Promise<void>}
 */
async function handleStreamingRequest(requestData, tabId) {
  try {
    // Get settings
    const settings = await getSettings();

    // Check if mock API is enabled
    if (settings.useMockApi) {
      return mockStreamingResponse(requestData, tabId);
    }

    // Get API URL
    const environment = settings.environment || 'prod';
    const apiUrl = getApiUrlForEnvironment(environment);

    // Get authentication token
    const userData = await getUserData();
    const authToken = userData?.apiToken;

    console.log('Background script - User data for API request:', userData ? 'Found' : 'Not found');
    console.log('Background script - Auth token for API request:', authToken ? 'Found' : 'Not found');

    // Prepare headers
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream'
    };

    // Add authorization header if token exists
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
      console.log('Background script - Added Authorization header to request');
    } else {
      console.warn('Background script - No auth token available for API request');
    }

    // Make API request
    const response = await fetch(`${apiUrl}/generate_stream`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      // For 429 errors, try to get the detailed error message from the response
      if (response.status === 429) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`API error: ${response.status} ${errorData.detail || response.statusText}`);
      } else {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
    }

    // Process the stream
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      buffer += decoder.decode(value, { stream: true });
      const lines = buffer.split('\n');
      buffer = lines.pop() || '';

      for (const line of lines) {
        if (line.trim() === '') continue;
        if (line.startsWith('data: ')) {
          const data = line.substring(6);

          // Send chunk to content script
          chrome.tabs.sendMessage(tabId, {
            action: 'streamResponseChunk',
            chunk: data
          });

          if (data === '[DONE]') return;
        }
      }
    }

    // Send done signal if not already sent
    chrome.tabs.sendMessage(tabId, {
      action: 'streamResponseChunk',
      chunk: '[DONE]'
    });
  } catch (error) {
    // Don't log to console, just send the error to the content script

    // Send error to content script
    chrome.tabs.sendMessage(tabId, {
      action: 'streamResponseError',
      error: error.message
    });

    // Don't throw the error, just return to avoid console errors
    return;
  }
}

/**
 * Copy text to clipboard
 * @param {string} text - Text to copy
 * @returns {Promise<void>}
 */
async function copyToClipboard(text) {
  try {
    // Get the current active tab
    const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
    if (!tabs || tabs.length === 0) {
      throw new Error('No active tab found');
    }

    // Execute script in the active tab to copy text
    await chrome.scripting.executeScript({
      target: { tabId: tabs[0].id },
      func: textToCopy => {
        // This function runs in the context of the page
        const textarea = document.createElement('textarea');
        textarea.value = textToCopy;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        document.body.appendChild(textarea);
        textarea.select();
        document.execCommand('copy');
        document.body.removeChild(textarea);
      },
      args: [text]
    });

    return Promise.resolve();
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return Promise.reject(new Error('Failed to copy text to clipboard: ' + error.message));
  }
}

/**
 * Get API URL for environment (centralized configuration for service worker)
 * @param {string} environment - The environment (local, dev, prod)
 * @returns {string} The API URL for the specified environment
 */
function getApiUrlForEnvironment(environment) {
  const apiUrls = {
    local: 'http://localhost:8000',
    dev: 'https://dev-api.replypal.com',
    prod: 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production'
  };
  return apiUrls[environment] || apiUrls.prod;
}

/**
 * Get settings from storage
 * @returns {Promise<Object>} Settings object
 */
function getSettings() {
  return new Promise((resolve) => {
    chrome.storage.local.get('settings', (data) => {
      resolve(data.settings || {
        environment: 'prod',
        saveHistory: true,
        useMockApi: false
      });
    });
  });
}

/**
 * Get user data from storage
 * @returns {Promise<Object|null>} User data object or null if not logged in
 */
function getUserData() {
  return new Promise((resolve) => {
    chrome.storage.local.get('user', (data) => {
      if (chrome.runtime.lastError) {
        console.error('Error retrieving user data:', chrome.runtime.lastError);
        resolve(null);
        return;
      }

      const userData = data.user || null;
      console.log('Retrieved user data from storage:', userData ? 'Found' : 'Not found');
      if (userData) {
        console.log('User ID:', userData.uid);
        console.log('API Token exists:', !!userData.apiToken);
      }

      resolve(userData);
    });
  });
}

/**
 * Mock generate response for testing
 * @param {Object} requestData - Request data
 * @returns {Promise<Object>} Mock response
 */
function mockGenerateResponse(requestData) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        response: `This is a mock response for: "${requestData.user_intent}"\n\nSelected text: "${requestData.selected_text}"\n\nTone: ${requestData.tone}\nPurpose: ${requestData.purpose}`
      });
    }, 1000);
  });
}

/**
 * Get API token from backend
 * @param {Object} userData - User data from Google authentication
 * @returns {Promise<Object>} Token response
 */
async function getApiToken(userData) {
  try {
    // Get settings
    const settings = await getSettings();

    // Get API URL
    const environment = settings.environment || 'prod';
    const apiUrl = getApiUrlForEnvironment(environment);

    // Make API request to get token
    const response = await fetch(`${apiUrl}/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify({
        uid: userData.uid,
        email: userData.email,
        name: userData.name,
        picture: userData.picture
      })
    });

    if (!response.ok) {
      throw new Error(`API error: ${response.status} ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error getting API token:', error);
    throw error;
  }
}

/**
 * Mock streaming response for testing
 * @param {Object} requestData - Request data
 * @param {number} tabId - Tab ID
 * @returns {Promise<void>}
 */
function mockStreamingResponse(requestData, tabId) {
  return new Promise((resolve) => {
    const mockResponse = `This is a mock streaming response for: "${requestData.user_intent}"\n\nSelected text: "${requestData.selected_text}"\n\nTone: ${requestData.tone}\nPurpose: ${requestData.purpose}`;

    // Split the response into chunks
    const chunks = mockResponse.split(' ');

    // Send chunks with delay
    let i = 0;
    const interval = setInterval(() => {
      if (i < chunks.length) {
        chrome.tabs.sendMessage(tabId, {
          action: 'streamResponseChunk',
          chunk: chunks[i] + ' '
        });
        i++;
      } else {
        clearInterval(interval);

        // Send done signal
        chrome.tabs.sendMessage(tabId, {
          action: 'streamResponseChunk',
          chunk: '[DONE]'
        });

        resolve();
      }
    }, 100);
  });
}
