/**
 * ReplyPal - Subscription Page Script
 * Handles Stripe integration and user interactions
 */

// Note: Configuration is now centralized in frontend/js/config.js
// This file uses window.ReplyPalConfig for all configuration needs

// DOM Elements
const subscribeBtn = document.getElementById('subscribeBtn');
const loginBtn = document.getElementById('loginBtn');
const faqItems = document.querySelectorAll('.faq-item');
const toast = document.getElementById('toast');
const loadingOverlay = document.getElementById('loadingOverlay');

// Stripe instance
let stripe;

// User data
let userData = null;

/**
 * Initialize the page
 */
async function init() {
  try {
    // Check for user data in URL fragment from Google OAuth redirect
    if (window.location.hash) {
      const hashParams = new URLSearchParams(window.location.hash.substring(1));
      if (hashParams.has('user')) {
        try {
          // Parse user data from URL fragment
          const userData = JSON.parse(hashParams.get('user'));

          // Store user data
          localStorage.setItem('replypal_user', JSON.stringify(userData));

          // Clean up the URL
          window.history.replaceState({}, document.title, window.location.pathname);

          // Show welcome message
          showToast(`Welcome ${userData.name || userData.email}!`, false);
        } catch (e) {
          console.error('Error parsing user data from URL:', e);
        }
      } else if (hashParams.has('error')) {
        // Show error message
        showToast(`Login failed: ${hashParams.get('error')}`, true);

        // Clean up the URL
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }

    // First check if user is logged in before doing anything else
    const isLoggedIn = await checkUserAuth();

    // If not logged in, redirect to login
    if (!isLoggedIn) {
      // Check if we're in the extension context
      const isExtension = window.location.href.includes('chrome-extension://') ||
                          !!localStorage.getItem('replypal_from_extension');

      if (isExtension) {
        // Try to auto-login using Chrome extension API
        await autoLoginFromExtension();
      } else {
        // Redirect to login page if not in extension context
        redirectToLogin();
        return; // Stop initialization
      }
    }

    // Continue with initialization only if logged in
    // Get Stripe publishable key from API
    const apiUrl = await window.ReplyPalConfig.getCurrentApiUrl();
    const response = await fetch(`${apiUrl}/subscription/plans`);

    if (!response.ok) {
      throw new Error('Failed to load subscription plans');
    }

    const plans = await response.json();
    const freePlan = plans.find(plan => plan.id === 'free');
    const basicPlan = plans.find(plan => plan.id === 'basic');

    if (!basicPlan) {
      throw new Error('Basic plan not found');
    }

    // Update the UI with plan information
    updatePlanUI(freePlan, basicPlan);

    // Initialize Stripe (Stripe is loaded from external script in HTML)
    stripe = Stripe(await getStripePublishableKey());

    // Set up event listeners
    setupEventListeners();
  } catch (error) {
    showToast(error.message, true);
    console.error('Initialization error:', error);
  }
}

/**
 * Update the UI with plan information
 */
function updatePlanUI(freePlan, basicPlan) {
  if (freePlan) {
    // Update free plan UI
    const freeCard = document.querySelector('.pricing-card.free');
    if (freeCard) {
      // Update price
      const priceEl = freeCard.querySelector('.price');
      if (priceEl) {
        priceEl.innerHTML = `$${freePlan.price}<span>/${freePlan.interval}</span>`;
      }

      // Update features
      const featuresList = freeCard.querySelector('.features-list');
      if (featuresList && freePlan.features && freePlan.features.length > 0) {
        featuresList.innerHTML = freePlan.features.map(feature =>
          `<li><i class="fa-solid fa-check"></i> ${feature}</li>`
        ).join('');
      }
    }
  }

  if (basicPlan) {
    // Update basic plan UI
    const basicCard = document.querySelector('.pricing-card.premium');
    if (basicCard) {
      // Update name
      const nameEl = basicCard.querySelector('h3');
      if (nameEl) {
        nameEl.textContent = basicPlan.name;
      }

      // Update price
      const priceEl = basicCard.querySelector('.price');
      if (priceEl) {
        priceEl.innerHTML = `$${basicPlan.price}<span>/${basicPlan.interval}</span>`;
      }

      // Update features
      const featuresList = basicCard.querySelector('.features-list');
      if (featuresList && basicPlan.features && basicPlan.features.length > 0) {
        featuresList.innerHTML = basicPlan.features.map(feature =>
          `<li><i class="fa-solid fa-check"></i> ${feature}</li>`
        ).join('');
      }
    }
  }
}

/**
 * Set up event listeners
 */
function setupEventListeners() {
  // Subscribe button
  subscribeBtn.addEventListener('click', handleSubscription);

  // Login button
  loginBtn.addEventListener('click', handleLogin);

  // FAQ accordion
  faqItems.forEach(item => {
    const question = item.querySelector('.faq-question');
    question.addEventListener('click', () => {
      item.classList.toggle('active');
    });
  });
}

/**
 * Handle subscription button click
 */
async function handleSubscription() {
  try {
    // Check if user is logged in
    if (!userData || !userData.apiToken) {
      showToast('Please log in to subscribe', true);
      return;
    }

    showLoading(true);

    // Get API URL using centralized configuration
    const apiUrl = await window.ReplyPalConfig.getCurrentApiUrl();

    // Get subscription plans
    const plansResponse = await fetch(`${apiUrl}/subscription/plans`, {
      headers: {
        'Authorization': `Bearer ${userData.apiToken}`
      }
    });

    if (!plansResponse.ok) {
      throw new Error('Failed to load subscription plans');
    }

    const plans = await plansResponse.json();
    const basicPlan = plans.find(plan => plan.id === 'basic');

    if (!basicPlan) {
      throw new Error('Basic subscription plan not found');
    }

    if (!basicPlan.stripe_price_id) {
      throw new Error('Stripe price ID not found for Basic plan. Please contact support.');
    }

    // Create checkout session
    const checkoutResponse = await fetch(`${apiUrl}/subscription/create-checkout-session`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${userData.apiToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ price_id: basicPlan.stripe_price_id })
    });

    if (!checkoutResponse.ok) {
      // Try to parse error response
      try {
        const errorData = await checkoutResponse.json();
        throw new Error(errorData.detail || `Error creating checkout session: ${checkoutResponse.status}`);
      } catch (e) {
        throw new Error(`Error creating checkout session: ${checkoutResponse.status}`);
      }
    }

    const checkoutData = await checkoutResponse.json();

    // Redirect to Stripe Checkout
    if (checkoutData.checkout_url) {
      // Show a message before redirecting
      showToast('Redirecting to secure payment page...', false);
      setTimeout(() => {
        window.location.href = checkoutData.checkout_url;
      }, 1000);
    } else {
      throw new Error('No checkout URL received');
    }
  } catch (error) {
    showToast(error.message, true);
    console.error('Subscription error:', error);
  } finally {
    showLoading(false);
  }
}

/**
 * Handle login button click
 */
function handleLogin() {
  // Check if we're in the extension context
  const isExtension = window.location.href.includes('chrome-extension://') ||
                      !!localStorage.getItem('replypal_from_extension');

  if (isExtension) {
    // Try to use Chrome extension API for login
    autoLoginFromExtension();
  } else {
    // Redirect to login page
    redirectToLogin();
  }
}

/**
 * Redirect to login page
 */
function redirectToLogin() {
  window.location.href = 'login.html';
}

/**
 * Check if user is authenticated
 * @returns {Promise<boolean>} True if user is authenticated, false otherwise
 */
async function checkUserAuth() {
  try {
    // Check if we have user data in localStorage
    const storedUser = localStorage.getItem('replypal_user');

    if (storedUser) {
      userData = JSON.parse(storedUser);

      // Update login button
      loginBtn.textContent = 'My Account';
      loginBtn.href = 'account.html';

      // Verify token is still valid
      const apiUrl = await window.ReplyPalConfig.getCurrentApiUrl();
      const response = await fetch(`${apiUrl}/auth/verify`, {
        headers: {
          'Authorization': `Bearer ${userData.apiToken}`
        }
      });

      if (!response.ok) {
        // Token is invalid, clear user data
        localStorage.removeItem('replypal_user');
        userData = null;

        // Reset login button
        loginBtn.textContent = 'Login';
        loginBtn.href = 'login.html';

        return false;
      }

      // Show the main content since user is authenticated
      showMainContent();
      return true;
    }

    return false;
  } catch (error) {
    console.error('Auth check error:', error);
    // Clear user data on error
    localStorage.removeItem('replypal_user');
    userData = null;
    return false;
  }
}

/**
 * Attempt to auto-login using Chrome extension API
 * @returns {Promise<boolean>} True if login successful, false otherwise
 */
async function autoLoginFromExtension() {
  return new Promise((resolve) => {
    try {
      // Check if we're in a Chrome extension context
      if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.sendMessage) {
        // Mark that we came from the extension
        localStorage.setItem('replypal_from_extension', 'true');

        // Try to use Chrome extension API for authentication
        chrome.runtime.sendMessage({ action: 'authenticateWithGoogle' }, (response) => {
          if (response && response.success && response.user) {
            // Store user data
            userData = {
              uid: response.user.id,
              displayName: response.user.name,
              email: response.user.email,
              photoURL: response.user.picture,
              apiToken: response.user.apiToken
            };

            localStorage.setItem('replypal_user', JSON.stringify(userData));

            // Update login button
            loginBtn.textContent = 'My Account';
            loginBtn.href = 'account.html';

            showToast(`Welcome ${userData.displayName || userData.email}!`, false);

            // Show the main content since user is authenticated
            showMainContent();

            // Continue initialization without reloading
            init();
            resolve(true);
          } else {
            console.error('Auto-login failed:', response?.error || 'Unknown error');
            showToast('Login failed. Please try again.', true);
            resolve(false);
          }
        });
      } else {
        console.warn('Not in a Chrome extension context, cannot use extension API');
        resolve(false);
      }
    } catch (error) {
      console.error('Auto-login error:', error);
      showToast('Login failed. Please try again.', true);
      resolve(false);
    }
  });
}

/**
 * Get Stripe publishable key
 */
async function getStripePublishableKey() {
  try {
    const apiUrl = await window.ReplyPalConfig.getCurrentApiUrl();
    const response = await fetch(`${apiUrl}/subscription/publishable-key`);

    if (!response.ok) {
      throw new Error('Failed to load Stripe publishable key');
    }

    const data = await response.json();
    return data.publishable_key || 'pk_test_51RMtuTQkxr8FpV1innlxTokLhLIn2GufKRedYnktG77BulktUwJf15o7uGjy5j27qrX961iLblmdUDMA5CvsDFOY00jo5H7zIB';
  } catch (error) {
    console.error('Error fetching Stripe publishable key:', error);
    // Fallback to the key from .env
    return 'pk_test_51RMtuTQkxr8FpV1innlxTokLhLIn2GufKRedYnktG77BulktUwJf15o7uGjy5j27qrX961iLblmdUDMA5CvsDFOY00jo5H7zIB';
  }
}

// Note: getApiUrl() function removed - now using centralized window.ReplyPalConfig.getCurrentApiUrl()

/**
 * Show toast notification
 */
function showToast(message, isError = false) {
  toast.textContent = message;
  toast.className = 'toast show';

  if (isError) {
    toast.classList.add('error');
  }

  setTimeout(() => {
    toast.classList.remove('show');
  }, 3000);
}

/**
 * Show/hide loading overlay
 */
function showLoading(show) {
  if (show) {
    loadingOverlay.classList.add('show');
  } else {
    loadingOverlay.classList.remove('show');
  }
}

// Initialize the page when DOM is loaded
document.addEventListener('DOMContentLoaded', init);

// Show main content after authentication is verified
function showMainContent() {
  const mainContent = document.querySelector('.main-content');
  if (mainContent) {
    mainContent.style.display = 'block';
  }
}
