<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ReplyPal Login</title>
    <link rel="stylesheet" href="styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Centralized Configuration -->
    <script src="../frontend/js/config.js"></script>
    <style>
      body {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        margin: 0;
        background-color: #f5f7ff;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
      }

      .login-container {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        padding: 30px;
        width: 300px;
        text-align: center;
      }

      .login-logo {
        margin-bottom: 20px;
        color: var(--primary-color);
      }

      .login-logo i {
        font-size: 48px;
        margin-bottom: 10px;
      }

      .login-logo h1 {
        font-size: 24px;
        margin: 0;
        color: var(--primary-color);
      }

      .login-btn {
        background-color: var(--primary-color);
        color: white;
        border: none;
        border-radius: 4px;
        padding: 12px 20px;
        font-size: 16px;
        cursor: pointer;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        transition: background-color 0.2s;
      }

      .login-btn:hover {
        background-color: #3a5bd9;
      }

      .login-message {
        margin-top: 20px;
        font-size: 14px;
        color: #666;
      }

      /* Toast notification */
      .toast {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s;
        z-index: 1000;
      }

      .toast.show {
        opacity: 1;
      }

      .toast.error {
        background-color: rgba(220, 53, 69, 0.9);
      }

      .toast.success {
        background-color: rgba(40, 167, 69, 0.9);
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <div class="login-logo">
        <i class="fa-solid fa-comment-dots"></i>
        <h1>ReplyPal</h1>
      </div>
      <button id="loginBtn" class="login-btn">
        <i class="fab fa-google"></i>
        Login with Google
      </button>
      <p class="login-message">Please login to use ReplyPal</p>
    </div>

    <!-- Toast notification -->
    <div id="toast" class="toast"></div>

    <script>
      document.addEventListener("DOMContentLoaded", () => {
        const loginBtn = document.getElementById("loginBtn");
        const toast = document.getElementById("toast");

        // Check if we're in the extension context
        const isExtension =
          window.location.href.includes("chrome-extension://") ||
          !!localStorage.getItem("replypal_from_extension");

        // Login button click handler
        loginBtn.addEventListener("click", () => {
          loginBtn.disabled = true;
          loginBtn.innerHTML =
            '<i class="fas fa-spinner fa-spin"></i> Logging in...';

          if (isExtension) {
            // Use Chrome extension API for authentication
            chrome.runtime.sendMessage(
              { action: "authenticateWithGoogle" },
              (response) => {
                if (response && response.success) {
                  showToast(
                    `Welcome ${response.user.name || response.user.email}!`,
                    "success"
                  );

                  // Store user data
                  localStorage.setItem(
                    "replypal_user",
                    JSON.stringify(response.user)
                  );

                  // Redirect back to subscription page
                  setTimeout(() => {
                    window.location.href = "index.html";
                  }, 1000);
                } else {
                  showToast(
                    "Login failed: " + (response?.error || "Unknown error"),
                    "error"
                  );
                  loginBtn.disabled = false;
                  loginBtn.innerHTML =
                    '<i class="fab fa-google"></i> Login with Google';
                }
              }
            );
          } else {
            // Web version login - redirect to API auth endpoint
            window.ReplyPalConfig.getCurrentApiUrl().then(apiUrl => {
              // Use the actual deployed URL for the redirect_uri
              const redirectUri = window.location.origin.includes("localhost")
                ? "http://replypal-subscription.s3-website-us-east-1.amazonaws.com/index.html"
                : window.location.origin + "/index.html";

              window.location.href = `${apiUrl}/auth/google-login?redirect_uri=${encodeURIComponent(
                redirectUri
              )}`;
            });
          }
        });

        // Show toast notification
        function showToast(message, type = "") {
          toast.textContent = message;
          toast.className = "toast show";

          if (type) {
            toast.classList.add(type);
          }

          setTimeout(() => {
            toast.classList.remove("show");
          }, 3000);
        }

        // Note: getApiUrl() function removed - now using centralized window.ReplyPalConfig.getCurrentApiUrl()
      });
    </script>
  </body>
</html>
