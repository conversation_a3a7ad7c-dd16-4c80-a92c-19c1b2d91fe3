/**
 * ReplyPal Environment Reset Script
 * 
 * This script resets the environment setting to 'prod' in Chrome storage.
 * Run this in the browser console on any ReplyPal extension page to fix
 * environment configuration issues.
 * 
 * Usage:
 * 1. Open the ReplyPal extension popup or settings page
 * 2. Open browser developer tools (F12)
 * 3. Go to Console tab
 * 4. Copy and paste this entire script
 * 5. Press Enter to execute
 */

(function() {
  console.log('ReplyPal Environment Reset Script');
  console.log('==================================');
  
  // Check if we're in a Chrome extension context
  if (typeof chrome === 'undefined' || !chrome.storage) {
    console.error('❌ This script must be run in a Chrome extension context');
    console.log('Please open the ReplyPal extension popup or settings page first');
    return;
  }
  
  // Get current settings
  chrome.storage.local.get('settings', function(data) {
    console.log('📋 Current settings:', data.settings);
    
    // Prepare new settings with environment set to 'prod'
    const currentSettings = data.settings || {};
    const newSettings = {
      ...currentSettings,
      environment: 'prod'
    };
    
    // Save updated settings
    chrome.storage.local.set({ settings: newSettings }, function() {
      if (chrome.runtime.lastError) {
        console.error('❌ Error updating settings:', chrome.runtime.lastError);
      } else {
        console.log('✅ Settings updated successfully!');
        console.log('📋 New settings:', newSettings);
        console.log('🔄 Please reload the extension or refresh the page to apply changes');
        
        // Also clear any localStorage environment setting
        try {
          localStorage.removeItem('replypal_environment');
          console.log('✅ Cleared localStorage environment setting');
        } catch (e) {
          console.log('ℹ️ No localStorage environment setting to clear');
        }
      }
    });
  });
})();
