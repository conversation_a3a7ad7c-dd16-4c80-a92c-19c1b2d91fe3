<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ReplyPal Configuration Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .test-section {
      margin: 20px 0;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .success {
      background-color: #d4edda;
      border-color: #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      border-color: #f5c6cb;
    }
    pre {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 3px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>ReplyPal Configuration Test</h1>
  
  <div id="test-results"></div>
  
  <script src="js/config.js"></script>
  <script>
    async function runTests() {
      const resultsDiv = document.getElementById('test-results');
      const tests = [];
      
      // Test 1: Check if ReplyPalConfig is loaded
      tests.push({
        name: 'Configuration Object Loaded',
        test: () => typeof window.ReplyPalConfig !== 'undefined',
        expected: true
      });
      
      // Test 2: Check API URLs
      tests.push({
        name: 'API URLs Available',
        test: () => window.ReplyPalConfig.API_URLS && 
                   window.ReplyPalConfig.API_URLS.local &&
                   window.ReplyPalConfig.API_URLS.dev &&
                   window.ReplyPalConfig.API_URLS.prod,
        expected: true
      });
      
      // Test 3: Check getApiUrl function
      tests.push({
        name: 'getApiUrl Function Works',
        test: () => {
          const localUrl = window.ReplyPalConfig.getApiUrl('local');
          const prodUrl = window.ReplyPalConfig.getApiUrl('prod');
          return localUrl === 'http://localhost:8000' && 
                 prodUrl === 'https://n9dk65q3fd.execute-api.us-east-1.amazonaws.com/production';
        },
        expected: true
      });
      
      // Test 4: Check default settings
      tests.push({
        name: 'Default Settings Available',
        test: () => window.ReplyPalConfig.DEFAULT_SETTINGS &&
                   window.ReplyPalConfig.DEFAULT_SETTINGS.environment === 'prod',
        expected: true
      });
      
      // Test 5: Check subscription URL
      tests.push({
        name: 'Subscription URL Available',
        test: () => {
          const url = window.ReplyPalConfig.getSubscriptionUrl();
          return url && url.includes('replypal-subscription');
        },
        expected: true
      });
      
      // Test 6: Check getCurrentSettings function (mock chrome.storage)
      tests.push({
        name: 'getCurrentSettings Function Available',
        test: () => typeof window.ReplyPalConfig.getCurrentSettings === 'function',
        expected: true
      });
      
      // Run tests and display results
      let allPassed = true;
      
      for (const test of tests) {
        const testDiv = document.createElement('div');
        testDiv.className = 'test-section';
        
        try {
          const result = test.test();
          const passed = result === test.expected;
          
          if (!passed) allPassed = false;
          
          testDiv.className += passed ? ' success' : ' error';
          testDiv.innerHTML = `
            <h3>${test.name}</h3>
            <p><strong>Status:</strong> ${passed ? 'PASSED' : 'FAILED'}</p>
            <p><strong>Result:</strong> ${result}</p>
            <p><strong>Expected:</strong> ${test.expected}</p>
          `;
        } catch (error) {
          allPassed = false;
          testDiv.className += ' error';
          testDiv.innerHTML = `
            <h3>${test.name}</h3>
            <p><strong>Status:</strong> ERROR</p>
            <p><strong>Error:</strong> ${error.message}</p>
          `;
        }
        
        resultsDiv.appendChild(testDiv);
      }
      
      // Summary
      const summaryDiv = document.createElement('div');
      summaryDiv.className = 'test-section ' + (allPassed ? 'success' : 'error');
      summaryDiv.innerHTML = `
        <h2>Test Summary</h2>
        <p><strong>Overall Status:</strong> ${allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'}</p>
        <p><strong>Configuration Status:</strong> ${allPassed ? 'Ready for use' : 'Needs attention'}</p>
      `;
      resultsDiv.insertBefore(summaryDiv, resultsDiv.firstChild);
      
      // Display configuration details
      const configDiv = document.createElement('div');
      configDiv.className = 'test-section';
      configDiv.innerHTML = `
        <h3>Configuration Details</h3>
        <pre>${JSON.stringify({
          API_URLS: window.ReplyPalConfig.API_URLS,
          DEFAULT_SETTINGS: window.ReplyPalConfig.DEFAULT_SETTINGS,
          EXTERNAL_URLS: window.ReplyPalConfig.EXTERNAL_URLS,
          USAGE_LIMITS: window.ReplyPalConfig.USAGE_LIMITS
        }, null, 2)}</pre>
      `;
      resultsDiv.appendChild(configDiv);
    }
    
    // Run tests when page loads
    document.addEventListener('DOMContentLoaded', runTests);
  </script>
</body>
</html>
